<?php

namespace Totaltools\Catalog\Plugin\Catalog\Model;

class Product
{
    /**
     * @var \Psr\Log\LoggerInterface
     */
    protected $logger;

    /**
     * Constructor
     *
     * @param \Psr\Log\LoggerInterface $logger
     */
    public function __construct(
        \Psr\Log\LoggerInterface $logger
    ){
        $this->logger  = $logger;
    }


    public function aroundIsSalable(\Magento\Catalog\Model\Product $subject, \Closure $proceed)
    {
        if ((bool) $subject->getNotForSale() === true) {
            return false;
        }

        return $proceed();
    } 
}
